{"name": "easy-eats", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "^12.23.12", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-leaflet": "^5.0.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.0", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "22.15.30", "@types/react": "19.1.6", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "5.8.3"}}