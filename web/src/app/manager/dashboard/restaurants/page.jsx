"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { MapPin, Users, Star, Search, Eye } from "lucide-react";
import Link from "next/link";

export default function RestaurantsPage() {
  const [searchTerm, setSearchTerm] = useState("");

  const restaurants = [
    {
      id: 1,
      name: "Blantyre Bites",
      location: "Main St, Blantyre City Centre",
      status: "Active",
      staff: 15,
      rating: 4.8,
      revenue: "MWK12,450",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 2,
      name: "Lilongwe Mall Eatery",
      location: "Gateway Mall, Lilongwe",
      status: "Active",
      staff: 12,
      rating: 4.6,
      revenue: "MWK8,920",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 3,
      name: "Chileka Airport Cafe",
      location: "Chileka International Airport, Blantyre",
      status: "Active",
      staff: 18,
      rating: 4.4,
      revenue: "MWK15,680",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 4,
      name: "Lake Shore Lounge",
      location: "Lake Malawi, Mangochi",
      status: "Maintenance",
      staff: 10,
      rating: 4.9,
      revenue: "MWK6,750",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 5,
      name: "Chancellor College Dining",
      location: "Chancellor College, Zomba",
      status: "Active",
      staff: 14,
      rating: 4.5,
      revenue: "MWK9,340",
      image: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 6,
      name: "Capital Business Eatery",
      location: "Capital City, Lilongwe",
      status: "Active",
      staff: 16,
      rating: 4.7,
      revenue: "MWK11,230",
      image: "/placeholder.svg?height=200&width=300",
    },
  ];

  const filteredRestaurants = restaurants.filter(
    (restaurant) =>
      restaurant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      restaurant.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800";
      case "Maintenance":
        return "bg-yellow-100 text-yellow-800";
      case "Closed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                My Restaurants
              </h1>
              <p className="text-gray-600">
                Manage all your restaurant locations
              </p>
            </div>
            <Link href="/manager/dashboard/restaurants/create">
              <Button variant="outline">+ Add New Branch</Button>
            </Link>
          </div>

          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search restaurants..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Restaurants Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRestaurants.map((restaurant) => (
            <Card
              key={restaurant.id}
              className="overflow-hidden hover:shadow-lg transition-shadow"
            >
              <div className="aspect-video bg-gray-200 relative">
                <img
                  src={restaurant.image || "/placeholder.svg"}
                  alt={restaurant.name}
                  className="w-full h-full object-cover"
                />
                <Badge
                  className={`absolute top-3 right-3 ${getStatusColor(
                    restaurant.status
                  )}`}
                >
                  {restaurant.status}
                </Badge>
              </div>

              <CardHeader>
                <CardTitle className="text-xl">{restaurant.name}</CardTitle>
                <CardDescription className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  {restaurant.location}
                </CardDescription>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        {restaurant.staff} Staff
                      </span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm font-medium">
                        {restaurant.rating}
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-600">Monthly Revenue</p>
                      <p className="text-lg font-semibold text-green-600">
                        {restaurant.revenue}
                      </p>
                    </div>
                  </div>

                  <Link
                    href={`/manager/dashboard/restaurants/${restaurant.id}`}
                    className="w-full"
                  >
                    <Button className="w-full mt-4">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredRestaurants.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              No restaurants found matching your search.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
