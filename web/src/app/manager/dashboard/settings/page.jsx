import { But<PERSON> } from "@/components/ui/button";
import { Edit, Pencil } from "lucide-react";
import Image from "next/image";

export default function SettingsPage(){
    return(
        <div className="m-4 flex-col border border-gray-300 rounded-md max-w-xxl p-4">
            <div className="text-xl my-2 pb-4 font-bold border-b border-gray-300 w-full text-center">
                Domain Settings
            </div>
            <div className="px-6 w-full border-b border-gray-300">
                <div className="flex">
                    <div className="flex">
                        <div className="rounded-full border border-gray-300 m-3 shadow-sm">
                            <Image src="/logo3.png" height={200} width={200}/>
                        </div>
                        <Button variant="outline" className="border-none fixed translate-y-45 translate-x-47">
                            <Edit className="text-gray-700"/>
                        </Button>
                    </div>
                    <div className="mx-4 flex-1 my-auto text-center">
                        <div className="text-xl font-bold text-gray-700">Burger Bros co.</div>
                        <div className="text-gray-500 text-sm">Apa pakhala some random moto or description if they have one.
                            <Button variant="outline" className="border-none mx-1 shadow-none">
                                <Pencil className="text-gray-700"/>
                            </Button></div>
                    </div>
                </div>
            </div>
        </div>
    )
}