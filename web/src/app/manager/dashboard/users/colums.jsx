"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { toast } from "sonner";

const getAuthHeaders = () => {
  const token =
    localStorage.getItem("managerToken") ||
    localStorage.getItem("accessToken") ||
    localStorage.getItem("adminToken");

  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    // Add Host header to simulate tenant subdomain
    Host: "test-restaurant.localhost:8000",
  };

  const restaurantId = localStorage.getItem("restaurantId");
  const schemaName = localStorage.getItem("schemaName");
  if (restaurantId) {
    headers["X-Restaurant-ID"] = restaurantId;
  }
  if (schemaName) {
    headers["X-Schema-Name"] = schemaName;
  } else {
    // Default to test tenant for development
    headers["X-Schema-Name"] = "test_restaurant";
  }

  return headers;
};

const getApiUrl = (endpoint) => {
  const baseUrl =
    process.env.NEXT_PUBLIC_BACKEND_URL ||
    process.env.BACKEND_URL ||
    "http://localhost:8000/";
  //"https://dp.technixlabs.org/";

  return `${baseUrl}api/tenant/${endpoint}`;
};

const handleDeleteUser = async (userId, userName) => {
  if (
    !confirm(
      `Are you sure you want to delete ${userName}? This action cannot be undone.`
    )
  ) {
    return;
  }

  try {
    const response = await fetch(getApiUrl(`staff/${userId}/`), {
      method: "DELETE",
      headers: getAuthHeaders(),
    });

    if (response.ok) {
      toast.success("Staff member deleted successfully");
      // Refresh the page to update the list
      window.location.reload();
    } else {
      console.error("Failed to delete user:", response.status);
      toast.error(`Failed to delete user: ${response.status}`);
    }
  } catch (error) {
    console.error("Network error deleting user:", error);
    toast.error("Network error");
  }
};

export const columns = [
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    filterable: true,
  },
  { accessorKey: "email", header: "Email", filterable: true },
  {
    accessorKey: "role",
    header: "Role",
    filterable: false,
    cell: ({ row }) => {
      const role = row.getValue("role");
      return <span className="capitalize">{role}</span>;
    },
  },
  {
    accessorKey: "username",
    header: "Username",
    filterable: false,
    cell: ({ row }) => {
      const username = row.getValue("username");
      return <span className="text-sm text-gray-600">{username}</span>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => (window.location.href = `users/${user.id}`)}
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDeleteUser(user.id, user.name)}
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
];
